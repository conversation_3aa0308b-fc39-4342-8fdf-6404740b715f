import psycopg2
import psycopg2.extras
import os
from dotenv import load_dotenv

load_dotenv()

class Database:
    def __init__(self):
        self.connection = None
        self.connect()
    
    def connect(self):
        """Connect to PostgreSQL database"""
        try:
            self.connection = psycopg2.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                database=os.getenv('DB_NAME', 'sastra'),
                user=os.getenv('DB_USER', 'postgres'),
                password=os.getenv('DB_PASSWORD', 'password'),
                port=os.getenv('DB_PORT', '5432')
            )
            self.connection.autocommit = True
            print("Database connected successfully")
        except Exception as e:
            print(f"Database connection error: {e}")
            self.connection = None
    
    def ensure_connection(self):
        """Ensure the connection is open, reconnect if necessary"""
        try:
            # Check if connection is None or closed
            if self.connection is None or self.connection.closed:
                print("Connection closed or not initialized, attempting to reconnect...")
                self.connect()
            # Test the connection with a simple query to ensure it's alive
            else:
                cursor = self.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
        except psycopg2.Error as e:
            print(f"Connection test failed: {e}, attempting to reconnect...")
            self.connect()
    
    def execute_query(self, query, params=None, fetch=False):
        """Execute a database query"""
        self.ensure_connection()
        if not self.connection:
            print("No database connection after ensure_connection")
            return None

        try:
            cursor = self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cursor.execute(query, params)

            if fetch:
                return cursor.fetchall()
            elif query.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            elif 'RETURNING' in query.upper():
                # Handle INSERT/UPDATE/DELETE with RETURNING clause
                return cursor.fetchall()
            else:
                return True

        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            print(f"Query: {query}")
            print(f"Params: {params}")
            return None
    
    def execute_query_one(self, query, params=None):
        """Execute a query and return one result"""
        self.ensure_connection()
        if not self.connection:
            print("No database connection after ensure_connection")
            return None
        
        try:
            cursor = self.connection.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cursor.execute(query, params)
            return cursor.fetchone()
        except psycopg2.Error as e:
            print(f"Query execution error: {e}")
            return None
    
    def close(self):
        """Close database connection"""
        if self.connection and not self.connection.closed:
            self.connection.close()
            print("Database connection closed")

def init_database():
    """Initialize database with UUID-based tables"""
    db = Database()
    
    if not db.connection:
        print("Failed to connect to database")
        return False
    
    print("Initializing database with UUID support...")
    
    # Enable UUID extension
    db.execute_query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    
    # Create tables with UUID primary keys
    tables = [
        """
        CREATE TABLE IF NOT EXISTS users (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            role VARCHAR(50) NOT NULL,
            first_name VARCHAR(100),
            last_name VARCHAR(100),
            email VARCHAR(100),
            phone VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS centers (
            id SERIAL PRIMARY KEY,
            center_code VARCHAR(20) UNIQUE NOT NULL,
            name VARCHAR(200) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS students (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            dob DATE,
            course VARCHAR(50),
            marks_10th DECIMAL(5,2),
            marks_12th DECIMAL(5,2),
            religion VARCHAR(50),
            center_code VARCHAR(20) REFERENCES centers(center_code),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS parents (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100),
            email VARCHAR(100),
            phone VARCHAR(20),
            relationship VARCHAR(20),
            student_id UUID REFERENCES students(id),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS faculty (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            dob DATE,
            center_code VARCHAR(20) REFERENCES centers(center_code),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS kota_teachers (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            username VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(100) NOT NULL,
            last_name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            course VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS teacher_center_mapping (
            id SERIAL PRIMARY KEY,
            teacher_id UUID REFERENCES kota_teachers(id),
            center_code VARCHAR(20) REFERENCES centers(center_code),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(teacher_id, center_code)
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS streaming_sessions (
            id VARCHAR(100) PRIMARY KEY,
            teacher_id UUID REFERENCES kota_teachers(id),
            title VARCHAR(200) NOT NULL,
            stream_key VARCHAR(200),
            ingest_server VARCHAR(500),
            playback_url VARCHAR(500),
            status VARCHAR(20) DEFAULT 'active',
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ended_at TIMESTAMP,
            recording_url VARCHAR(500)
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS stream_participants (
            id VARCHAR(100) PRIMARY KEY,
            session_id VARCHAR(100) REFERENCES streaming_sessions(id),
            faculty_id UUID REFERENCES faculty(id),
            joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            left_at TIMESTAMP,
            status VARCHAR(20) DEFAULT 'active',
            UNIQUE(session_id, faculty_id)
        )
        """,
        """
        CREATE TABLE IF NOT EXISTS approval_requests (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            request_type VARCHAR(50) NOT NULL,
            center_code VARCHAR(20) REFERENCES centers(center_code),
            request_data JSONB NOT NULL,
            status VARCHAR(20) DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_at TIMESTAMP
        )
        """
    ]
    
    for table_sql in tables:
        result = db.execute_query(table_sql)
        if result:
            print(f"Table created/verified successfully")
        else:
            print(f"Failed to create table")
            return False
    
    print("Database initialization completed successfully!")
    return True

if __name__ == "__main__":
    init_database()